<?php
namespace app\controller;

use support\Request;
use support\Log;
use support\think\Db;
use app\model\SchoolReport;
use app\model\SchoolInfo;
use Symfony\Component\Console\Event\ConsoleEvent;
use support\Redis;
class SchoolDetailController
{
    /**
     * 获取单个学校的详细信息（新版本）
     * 只获取当年的招生情况数据，如果没有数据则不返回招生情况
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolDetail(Request $request)
    {
        try {
            $params = $request->all();

            // 1. 验证必需参数
            if (empty($params['school_name'])) {
                return json([
                    'code' => 1,
                    'msg' => '学校名称不能为空',
                    'data' => null
                ]);
            }

            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            $schoolName = trim($params['school_name']);
            $reportId = trim($params['report_id']);
            if(preg_match('/^(.*?)\((.*?)\)$/u', $schoolName, $matches)){
                $schoolName = $matches[1];
                $college = $matches[2]; 
            }else{
                Log::error("院校字符串不合理");
            }

            if(empty($college)){
                return json([
                    'code' => 1,
                    'msg' => '学院名称不能为空',
                    'data' => null
                ]);
            }

            $storeSchoolIds = json_decode(Redis::get("report_".$reportId), true);
            $schoolInfo = [];
            foreach($storeSchoolIds as $v){
                if($v['school_name'] == $schoolName && $v['college'] == $college){
                    $schoolInfo = $v;
                }
            }

            // 2. 从ba_school_report表中获取报告信息
            $reportSchool = SchoolReport::where('id', $reportId)
                ->where('is_delete', 0)
                ->find();
                
            if (empty($reportSchool)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到报告信息',
                    'data' => null
                ]);
            }

            // 3. 根据学校名称和专业代码在ba_school_info表中查找学校信息
            // $schoolInfo = SchoolInfo::where('school_name', $schoolName)
            //     ->where('major_code', $reportSchool['target_major_code'])
            //     ->find();
                
            if (empty($schoolInfo)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到该学校的专业信息',
                    'data' => null
                ]);
            }

            $schoolId = $schoolInfo['id'];

            // 4. 获取学校基本信息
            $schoolBasicInfo = $this->getSchoolBasicInfo($schoolId);
            
            // 5. 获取学校详细信息（logo、标签等）
            $schoolDetailInfo = $this->getSchoolDetailInfo($schoolName);
            
            // 6. 获取当年的招生情况数据
            $currentYearAdmissionData = $this->getCurrentYearAdmissionData($schoolName, $schoolInfo['major_code'], $schoolId);
            
            // 7. 获取当年的复试名单
            $currentYearRetestList = $this->getCurrentYearRetestList($schoolId);
            // 8. 获取当年的录取名单
            $currentYearAdmissionList = $this->getCurrentYearAdmissionList($schoolId);

            // 9. 构建返回数据
            $responseData = [
                'school_name' => $schoolName,
                'school_id' => $schoolId,
                'basic_info' => $schoolBasicInfo,
                'school_info' => $schoolDetailInfo,
                'current_year_retest_list' => $currentYearRetestList,
                'current_year_admission_list' => $currentYearAdmissionList,
            ];

            // 只有当年有招生数据时才添加招生情况
            if (!empty($currentYearAdmissionData)) {
                $responseData['admission_data'] = $currentYearAdmissionData;
            }

            // 10. 构建学校列表项数据
            $schoolListItem = $this->buildSchoolListItem($schoolInfo, $reportSchool, $schoolDetailInfo, $currentYearRetestList);

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'info' => $responseData,
                    'item' => $schoolListItem,
                    'admission_data' => $currentYearAdmissionData
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取学校详细信息异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '获取学校详细信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取学校基本信息
     *
     * @param int $schoolId
     * @return array
     */
    private function getSchoolBasicInfo($schoolId)
    {
        try {
            $result = Db::name('school_basic_info')
                ->where('school_id', $schoolId)
                ->field('research_direction,exam_range,reference_books,retest_content,tuition_fee,study_years,accommodation,admission_requirements')
                ->find();

            if ($result) {
                return is_array($result) ? $result : $result->toArray();
            }

            return [];
        } catch (\Exception $e) {
            Log::error('获取学校基本信息异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取学校详细信息（logo、标签等）
     *
     * @param string $schoolName
     * @return array
     */
    private function getSchoolDetailInfo($schoolName)
    {
        try {
            $result = Db::name('school')
                ->where('name', $schoolName)
                ->where('is_delete', 0)
                ->field('name, id, logo, tag_211, tag_985, dual_class, provice_city as address, phone, home_site, zsb_site')
                ->find();

            if ($result) {
                $schoolData = is_array($result) ? $result : $result->toArray();
                
                // 处理logo
                if (empty($schoolData['logo'])) {
                    $schoolData['logo'] = "yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/dxt/schoolLogo/{$schoolData['id']}.jpeg";
                }

                // 转换标签为布尔值
                $schoolData['tag_211'] = (bool)$schoolData['tag_211'];
                $schoolData['tag_985'] = (bool)$schoolData['tag_985'];
                $schoolData['is_dual_class'] = (bool)$schoolData['dual_class'];

                return $schoolData;
            }

            return [];
        } catch (\Exception $e) {
            Log::error('获取学校详细信息异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取当年的招生情况数据
     * 只获取当年数据，如果没有数据则返回空数组
     *
     * @param string $schoolName
     * @param string $majorCode
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearAdmissionData($schoolName, $majorCode, $schoolId)
    {
        try {
            $admissionData = [];

            // 只获取最近一年的数据
            $currentYear = intval(date('Y'));
            $yearData = [
                'year' => $currentYear,
                'planCount' => 0,      // 招生计划
                'examCount' => 0,      // 一志愿复试
                'admitCount' => 0,     // 拟录取
                'ratio' => '0%',       // 录取比
                'studentCount' => 0,   // 调剂人数
                'highestScore' => 0,   // 最高分
                'lowestScore' => 0,    // 最低分
                'averageScore' => 0    // 平均分
            ];

            // 1. 获取招生计划数据 - 按planned_num由高到低排序取第一条
            $planData = Db::name('yzw_major')
                ->where('school_name', $schoolName)
                ->where('major_code', $majorCode)
                ->order('planned_num', 'desc')
                ->value('planned_num');

            if ($planData) {
                $yearData['planCount'] = intval($planData);
            }

            // 2. 从ba_school_info的admission字段获取录取数据
            $schoolInfo = Db::name('school_info')
                ->where('school_name', $schoolName)
                ->where('major_code', $majorCode)
                ->value('admission');

            if ($schoolInfo && $this->parseAdmissionString($schoolInfo, $yearData)) {
                // 成功解析admission字段
            } else {
                // 3. 如果admission字段不符合格式，从其他表计算
                $this->calculateAdmissionFromTables($schoolId, $currentYear, $yearData);
            }

            // 4. 计算分数统计
            $this->calculateScoreStatistics($schoolId, $currentYear, $yearData);

            // 只有当年有数据时才添加到结果中
            if ($yearData['planCount'] > 0 || $yearData['admitCount'] > 0) {
                $admissionData[] = $yearData;
            }

            return $admissionData;

        } catch (\Exception $e) {
            Log::error('获取招生情况数据异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 解析admission字段数据
     *
     * @param string $admissionStr admission字段内容
     * @param array &$yearData 年度数据引用
     * @return bool 是否成功解析
     */
    private function parseAdmissionString($admissionStr, &$yearData)
    {
        try {
            // 匹配格式："总录:11  一志愿:11   调剂:0  必达分:405"
            if (preg_match('/总录:(\d+).*?一志愿:(\d+).*?调剂:(\d+)/', $admissionStr, $matches)) {
                $yearData['admitCount'] = intval($matches[1]);
                $yearData['examCount'] = intval($matches[2]);
                $yearData['studentCount'] = intval($matches[3]);

                // 计算一志愿录取比
                if ($yearData['admitCount'] > 0) {
                    $ratio = ($yearData['examCount'] / $yearData['admitCount']) * 100;
                    $yearData['ratio'] = number_format($ratio, 1) . '%';
                }

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('解析admission数据异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 从其他表计算录取数据
     *
     * @param int $schoolId 学校ID
     * @param int $year 年份
     * @param array &$yearData 年度数据引用
     */
    private function calculateAdmissionFromTables($schoolId, $year, &$yearData)
    {
        try {
            // 从ba_admission_list计算录取数据
            $admissionStats = Db::name('admission_list')
                ->where('school_id', $schoolId)
                ->where('year', $year)
                ->field('COUNT(*) as total_count,
                        SUM(CASE WHEN first_choice_school = (SELECT school_name FROM ba_school_info WHERE id = ' . $schoolId . ' LIMIT 1) THEN 1 ELSE 0 END) as first_choice_count')
                ->find();

            if ($admissionStats) {
                $yearData['admitCount'] = intval($admissionStats['total_count']);
                $yearData['examCount'] = intval($admissionStats['first_choice_count']);
                $yearData['studentCount'] = $yearData['admitCount'] - $yearData['examCount'];

                // 计算一志愿录取比
                if ($yearData['admitCount'] > 0) {
                    $ratio = ($yearData['examCount'] / $yearData['admitCount']) * 100;
                    $yearData['ratio'] = number_format($ratio, 1) . '%';
                }
            }

            // 从ba_retest_list获取复试人数（如果需要的话）
            $retestCount = Db::name('retest_list')
                ->where('school_id', $schoolId)
                ->where('year', $year)
                ->count();

            if ($retestCount > 0 && $yearData['examCount'] == 0) {
                $yearData['examCount'] = $retestCount;
            }

        } catch (\Exception $e) {
            Log::error('从其他表计算录取数据异常: ' . $e->getMessage());
        }
    }

    /**
     * 计算分数统计
     *
     * @param int $schoolId 学校ID
     * @param int $year 年份
     * @param array &$yearData 年度数据引用
     */
    private function calculateScoreStatistics($schoolId, $year, &$yearData)
    {
        try {
            // 从ba_admission_list计算分数统计
            $scoreStats = Db::name('admission_list_backup')
                ->where('school_id', $schoolId)
                ->where('year', $year)
                ->field('MAX(CAST(initial_score AS DECIMAL(10,2))) as max_score,
                        MIN(CAST(initial_score AS DECIMAL(10,2))) as min_score,
                        AVG(CAST(initial_score AS DECIMAL(10,2))) as avg_score')
                ->find();

            if ($scoreStats) {
                $yearData['highestScore'] = intval($scoreStats['max_score'] ?? 0);
                $yearData['lowestScore'] = intval($scoreStats['min_score'] ?? 0);
                $yearData['averageScore'] = intval($scoreStats['avg_score'] ?? 0);
            }

        } catch (\Exception $e) {
            Log::error('计算分数统计异常: ' . $e->getMessage());
        }
    }

    /**
     * 获取当年的复试名单
     *
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearRetestList($schoolId)
    {
        try {
            $currentYear = intval(date('Y'));

            $retestList = Db::name('retest_list_backup')
                ->where('school_id', $schoolId)
                ->where('year', $currentYear)
                ->field('name, politics_score, english_score, major1_score, major2_score, initial_score, admission_status')
                ->order('initial_score', 'desc')
                ->select();

            return [
                'list' => $retestList ? $retestList->toArray() : [],
                'year' => $currentYear,
                'count' => $retestList ? count($retestList) : 0
            ];

        } catch (\Exception $e) {
            Log::error('获取当年复试名单异常: ' . $e->getMessage());
            return [
                'list' => [],
                'year' => null,
                'count' => 0
            ];
        }
    }

    /**
     * 获取当年的录取名单
     *
     * @param int $schoolId
     * @return array
     */
    private function getCurrentYearAdmissionList($schoolId)
    {
        try {
            $currentYear = intval(date('Y'));

            $admissionList = Db::name('admission_list_backup')
                ->where('school_id', $schoolId)
                ->where('year', $currentYear)
                ->field('name, initial_score, retest_score, total_score, first_choice_school')
                ->order('total_score', 'desc')
                ->select();
            return [
                'list' => $admissionList ? $admissionList->toArray() : [],
                'year' => $currentYear,
                'count' => $admissionList ? count($admissionList) : 0
            ];

        } catch (\Exception $e) {
            Log::error('获取当年录取名单异常: ' . $e->getMessage());
            return [
                'list' => [],
                'year' => null,
                'count' => 0
            ];
        }
    }

    /**
     * 构建学校列表项数据
     *
     * @param array $schoolInfo
     * @param array $reportSchool
     * @param array $schoolDetailInfo
     * @param array $currentYearRetestList
     * @return array
     */
    private function buildSchoolListItem($schoolInfo, $reportSchool, $schoolDetailInfo, $currentYearRetestList)
    {
        try {
            // 获取最低分数
            $minScore = 0;
            if (!empty($currentYearRetestList['list']) && isset($currentYearRetestList['list'][0]['initial_score'])) {
                $minScore = intval($currentYearRetestList['list'][0]['initial_score']);
            } elseif (isset($schoolInfo['must_reach_score'])) {
                $minScore = intval($schoolInfo['must_reach_score']);
            }

            return [
                'id' => intval($schoolInfo['id']),
                'school_name' => $schoolInfo['school_name'],
                'region' => $this->getRegionByProvince($schoolDetailInfo['address'] ?? '未知'),
                'city' => $this->extractCityFromAddress($schoolDetailInfo['address'] ?? '未知'),
                'college' => $schoolInfo['college'] ?? '',
                'major_name' => $schoolInfo['major_name'] ?? '',
                'major_code' => $schoolInfo['major_code'] ?? '',
                'min_score' => $minScore,
                'score_diff' => intval($reportSchool['total_score'] ?? 0) - $minScore,
                'tag_985' => (bool)($schoolDetailInfo['tag_985'] ?? false),
                'tag_211' => (bool)($schoolDetailInfo['tag_211'] ?? false),
                'tag_double' => (bool)($schoolDetailInfo['is_dual_class'] ?? false)
            ];

        } catch (\Exception $e) {
            Log::error('构建学校列表项数据异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 根据省份获取地区（A区/B区）
     *
     * @param string $address
     * @return string
     */
    private function getRegionByProvince($address)
    {
        // A区省份
        $aRegionProvinces = [
            '北京', '天津', '河北', '山西', '辽宁', '吉林', '黑龙江', '上海', '江苏',
            '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南', '广东',
            '重庆', '四川', '陕西'
        ];

        foreach ($aRegionProvinces as $province) {
            if (strpos($address, $province) !== false) {
                return 'A区';
            }
        }

        return 'B区';
    }

    /**
     * 从地址中提取城市名称
     *
     * @param string $address
     * @return string
     */
    private function extractCityFromAddress($address)
    {
        // 简单的城市提取逻辑
        if (preg_match('/(.+?省)?(.+?市)/', $address, $matches)) {
            return $matches[2] ?? '未知';
        }
        return '未知';
    }

    /**
     * 获取院校总览数据
     * 从ba_report_info表获取is_high_recommend=0的记录
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolOverview(Request $request)
    {
        try {
            $params = $request->all();

            // 验证必需参数
            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            $reportId = trim($params['report_id']);

            // 获取报告信息以便计算分差
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到报告信息',
                    'data' => null
                ]);
            }

            $totalScore = intval($report['total_score']);
            $majorCode = $report['target_major_code'];

            // 从ba_report_info表获取院校总览数据（is_high_recommend = 0）
            $reportInfoList = Db::name('report_info')
                ->where('report_id', $reportId)
                ->where('is_high_recommend', 0)
                ->where('status', 1)
                ->field('school_id, school_name, reason_recommendation')
                ->order('id', 'asc')
                ->select();

            if (empty($reportInfoList)) {
                return json([
                    'code' => 0,
                    'msg' => 'success',
                    'data' => [
                        'school_list' => [],
                        'high_recommend' => null
                    ]
                ]);
            }

            $schoolList = [];
            $schoolNames = []; // 用于记录已添加的学校名称，避免重复
            $index = 1;

            // 处理每个报告信息记录
            foreach ($reportInfoList as $reportInfo) {
                $schoolId = $reportInfo['school_id'];
                $schoolName = $reportInfo['school_name'];

                // 检查学校是否已经添加过（避免重复）
                if (in_array($schoolName, $schoolNames)) {
                    continue;
                }

                // 查询学校详细信息
                $schoolInfo = Db::name('school_info')
                    ->where('school_name', $schoolName)
                    ->where('major_code', $majorCode)
                    ->find();

                if ($schoolInfo) {
                    // 如果是对象，转换为数组
                    if (is_object($schoolInfo) && method_exists($schoolInfo, 'toArray')) {
                        $schoolInfoData = $schoolInfo->toArray();
                    } elseif (is_array($schoolInfo)) {
                        $schoolInfoData = $schoolInfo;
                    } else {
                        continue; // 跳过无效数据
                    }

                    // 计算分差
                    $scoreDiff = $totalScore - $schoolInfoData['must_reach_score'];

                    // 确定地区
                    $region = $this->getRegionByProvince($schoolInfoData['province']);

                    // 确定城市（从省份中提取或使用area字段）
                    $city = $schoolInfoData['area'] ?? $schoolInfoData['province'];

                    // 处理学院信息
                    $college = $schoolInfoData['college'] ?? '信息科学与技术学院';

                    // 处理学校标签
                    $tags = [];
                    if (strpos($schoolInfoData['school_type'], '985') !== false) {
                        $tags[] = '985';
                    }
                    if (strpos($schoolInfoData['school_type'], '211') !== false) {
                        $tags[] = '211';
                    }
                    if (strpos($schoolInfoData['school_type'], '双一流') !== false) {
                        $tags[] = '双一流';
                    }

                    // 添加学校到列表
                    $schoolList[] = [
                        'id' => $index,
                        'school_name' => $schoolInfoData['school_name'],
                        'region' => $region,
                        'city' => $city,
                        'college' => $college,
                        'major_name' => $schoolInfoData['major_name'],
                        'major_code' => $schoolInfoData['major_code'] ?? $majorCode,
                        'min_score' => $schoolInfoData['must_reach_score'],
                        'score_diff' => $scoreDiff,
                        'tags' => $tags,
                        'tag_985' => in_array('985', $tags),
                        'tag_211' => in_array('211', $tags),
                        'tag_double' => in_array('双一流', $tags)
                    ];
                } else {
                    // 如果数据库中没有找到，使用基本信息
                    $schoolList[] = [
                        'id' => $index,
                        'school_name' => $schoolName,
                        'region' => '未知',
                        'city' => '未知',
                        'college' => '未知',
                        'major_name' => '未知',
                        'major_code' => $majorCode,
                        'min_score' => 0,
                        'score_diff' => 0,
                        'tags' => [],
                        'tag_985' => false,
                        'tag_211' => false,
                        'tag_double' => false
                    ];
                }

                // 记录已添加的学校名称
                $schoolNames[] = $schoolName;
                $index++;
            }

            // 获取高性价比推荐院校（is_high_recommend = 1）
            $highRecommendInfo = Db::name('report_info')
                ->where('report_id', $reportId)
                ->where('is_high_recommend', 1)
                ->where('status', 1)
                ->field('school_name, reason_recommendation')
                ->find();

            $highRecommendData = null;
            if ($highRecommendInfo) {
                $highRecommendData = [
                    'school_name' => $highRecommendInfo['school_name'],
                    'reason' => $highRecommendInfo['reason_recommendation']
                ];
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'school_list' => $schoolList,
                    'high_recommend' => $highRecommendData
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取院校总览数据异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '获取院校总览数据失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取报告详情数据
     * 参考ba_report_info、ba_school_report表和RemoteController.php的getSchoolDetail方法
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getReportDetail(Request $request)
    {
        try {
            $params = $request->all();

            // 验证必需参数
            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            $reportId = trim($params['report_id']);

            // 1. 获取报告基本信息
            $report = SchoolReport::where('id', $reportId)->where('is_delete', 0)->find();
            if (empty($report)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到报告信息',
                    'data' => null
                ]);
            }

            // 2. 从ba_report_info表获取推荐院校数据
            $reportSchoolInfoList = Db::name('report_info')
                ->where('report_id', $reportId)
                ->where('status', 1)
                ->order('is_high_recommend', 'asc')
                ->order('id', 'asc')
                ->select();

            if (empty($reportSchoolInfoList)) {
                return json([
                    'code' => 0,
                    'msg' => 'success',
                    'data' => [
                        'recommend_list' => [],
                        'high_recommend_list' => [],
                        'school_list' => []
                    ]
                ]);
            }

            $recommendList = [];
            $highRecommendList = [];
            $schoolList = [];
            $totalScore = intval($report['total_score']);
            //$majorCode = $report['target_major_code'];

            // 3. 处理每个推荐院校
            foreach ($reportSchoolInfoList as $reportInfo) {
               
                $schoolId = $reportInfo['school_id'];

                //每所院校majorCode 都不一样

                $reportSchoolInfo  =  SchoolInfo::where('id', $schoolId)->find();
                $schoolName = $reportSchoolInfo['school_name'];
                $majorCode = $reportSchoolInfo['major_code'];
                $majorName = $reportSchoolInfo['major_name'];
                $collegeName = $reportSchoolInfo['college'];
                // 获取学校详细信息
                $schoolDetailData = $this->getSchoolDetailData($schoolName, $schoolId, $majorCode, $reportInfo, $majorName, $collegeName);

                if ($reportInfo['is_high_recommend'] == 1) {
                    // 高性价比推荐院校
                    $highRecommendList = [
                        'school_name' => $schoolName,
                        'school_id' => $schoolId,
                        'major_name' => $reportSchoolInfo['major_name'],
                        'reason' => $reportInfo['reason_recommendation']
                    ];
                } else {
                    // 普通推荐院校
                    $recommendList[] = array_merge($schoolDetailData, [
                        'difficulty_analysis' => $reportInfo['competition_difficulty'],
                        'suggest' => $reportInfo['suggestions'],
                        'reason' => $reportInfo['reason_recommendation']
                    ]);

                    // 构建院校总览列表项
                    $schoolListItem = $this->buildSchoolListItemNew($schoolDetailData, $report, $totalScore);
                    if ($schoolListItem) {
                        $schoolList[] = $schoolListItem;
                    }
                }
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => [
                    'recommend_list' => $recommendList,
                    'high_recommend_list' => $highRecommendList,
                    'school_list' => $schoolList
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取报告详情数据异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '获取报告详情数据失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取学校详细数据
     *
     * @param string $schoolName
     * @param int $schoolId
     * @param string $majorCode
     * @param array $reportInfo
     * @return array
     */
    private function getSchoolDetailData($schoolName, $schoolId, $majorCode, $reportInfo, $majorName, $collegeName)
    {
        try {
            // 1. 获取学校基本信息
            $schoolBasicInfo = $this->getSchoolBasicInfo($schoolId);

            // 2. 获取学校详细信息（logo、标签等）
            $schoolDetailInfo = $this->getSchoolDetailInfo($schoolName);

            // 3. 获取当年的复试名单
            $currentYearRetestList = $this->getCurrentYearRetestList($schoolId);

            // 4. 获取当年的录取名单
            $currentYearAdmissionList = $this->getCurrentYearAdmissionList($schoolId);

            // 5. 获取当年的招生情况数据
            $currentYearAdmissionData = $this->getCurrentYearAdmissionData($schoolName, $majorCode, $schoolId);

            return [
                'school_name' => $schoolName."(".$collegeName.")",
                'school_id' => $schoolId,
                'major_name' => $majorName ?? '',
                'basic_info' => array_merge($schoolBasicInfo, [
                    'score_formula' => $reportInfo['score_formula'] ?? '',
                    'study_years' => $reportInfo['study_years'] ?? '',
                    'tuition_fee' => $reportInfo['tuition_fee'] ?? '',
                    'exam_range' => $reportInfo['exam_subjects'] ?? '',
                    'reference_books' => $reportInfo['reference_books'] ?? '',
                    'retest_content' => $reportInfo['retest_content'] ?? '',
                    'admission_requirements' => $reportInfo['admission_requirements'] ?? '',
                    'retest_score_requirement'=>$reportInfo['retest_score_requirement']??""
                ]),
                'school_info' => $schoolDetailInfo,
                'current_year_retest_list' => $currentYearRetestList,
                'current_year_admission_list' => $currentYearAdmissionList,
                'admission_data' => $currentYearAdmissionData
            ];

            

        } catch (\Exception $e) {
            Log::error('获取学校详细数据异常: ' . $e->getMessage());
            return [
                'school_name' => $schoolName,
                'school_id' => $schoolId,
                'basic_info' => [],
                'school_info' => [],
                'current_year_retest_list' => ['list' => [], 'year' => null, 'count' => 0],
                'current_year_admission_list' => ['list' => [], 'year' => null, 'count' => 0],
                'admission_data' => []
            ];
        }
    }

    /**
     * 构建院校总览列表项（新版本）
     *
     * @param array $schoolDetailData
     * @param array $report
     * @param int $totalScore
     * @return array|null
     */
    private function buildSchoolListItemNew($schoolDetailData, $report, $totalScore)
    {   
        try {
            $schoolName = $schoolDetailData['school_name'];
            $schoolId = $schoolDetailData['school_id'];
            // 查询学校信息获取分数线等数据
            $schoolInfo = Db::name('school_info')
                ->where('id', $schoolId)
                ->find();

            if (!$schoolInfo) {
                return null;
            }

            // 获取最低分数
            $minScore = intval($schoolInfo['must_reach_score'] ?? 0);
            if ($minScore == 0 && !empty($schoolDetailData['current_year_retest_list']['list'])) {
                $retestList = $schoolDetailData['current_year_retest_list']['list'];
                if (isset($retestList[0]['initial_score'])) {
                    $minScore = intval($retestList[0]['initial_score']);
                }
            }

            // 计算分数差
            $scoreDiff = $totalScore - $minScore;

            return [
                'id' => intval($schoolId),
                'school_name' => $schoolInfo['school_name'],
                'region' => $this->getRegionByProvince($schoolDetailData['school_info']['address'] ?? '未知'),
                'city' => $this->extractCityFromAddress($schoolDetailData['school_info']['address'] ?? '未知'),
                'college' => $schoolInfo['college'] ?? '',
                'major_name' =>  $schoolInfo['major_name'] ?? '',
                'major_code' =>  $schoolInfo['major_code'] ?? '',
                'min_score' => $minScore,
                'score_diff' => $scoreDiff,
                'tag_985' => (bool)($schoolDetailData['school_info']['tag_985'] ?? false),
                'tag_211' => (bool)($schoolDetailData['school_info']['tag_211'] ?? false),
                'tag_double' => (bool)($schoolDetailData['school_info']['is_dual_class'] ?? false)
            ];

        } catch (\Exception $e) {
            Log::error('构建院校总览列表项异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 添加学校后获取学校详细信息
     * 主要用于添加学校后获取基本信息，包括从ba_school_basic_info表获取的详细字段
     *
     * @param Request $request
     * @return \support\Response
     */
    public function getSchoolDetailAfterAddSchool(Request $request)
    {
        try {
            $params = $request->all();

            // 1. 验证必需参数
            if (empty($params['school_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '学校ID不能为空',
                    'data' => null
                ]);
            }

            if (empty($params['report_id'])) {
                return json([
                    'code' => 1,
                    'msg' => '报告ID不能为空',
                    'data' => null
                ]);
            }

            $schoolId = intval($params['school_id']);
            $reportId = trim($params['report_id']);

            // 2. 获取学校基本信息（从ba_school_basic_info表）
            $schoolBasicInfo = $this->getSchoolBasicInfoForAddSchool($schoolId);

            // 3. 获取学校详细信息（logo、标签等）
            $schoolInfo = SchoolInfo::where('id', $schoolId)->find();
            if (empty($schoolInfo)) {
                return json([
                    'code' => 1,
                    'msg' => '未找到学校信息',
                    'data' => null
                ]);
            }

            $schoolName = $schoolInfo['school_name'];
            $schoolDetailInfo = $this->getSchoolDetailInfo($schoolName);

            // 4. 获取当年的招生情况数据
            $currentYearAdmissionData = $this->getCurrentYearAdmissionData($schoolName, $schoolInfo['major_code'], $schoolId);

            // 5. 获取当年的复试名单
            $currentYearRetestList = $this->getCurrentYearRetestList($schoolId);

            // 6. 获取当年的录取名单
            $currentYearAdmissionList = $this->getCurrentYearAdmissionList($schoolId);

            // 7. 构建返回数据
            $responseData = [
                'school_name' => $schoolName,
                'school_id' => $schoolId,
                'basic_info' => $schoolBasicInfo,
                'school_info' => $schoolDetailInfo,
                'current_year_retest_list' => $currentYearRetestList,
                'current_year_admission_list' => $currentYearAdmissionList,
            ];

            // 只有当年有招生数据时才添加招生情况
            if (!empty($currentYearAdmissionData)) {
                $responseData['admission_data'] = $currentYearAdmissionData;
            }

            return json([
                'code' => 0,
                'msg' => 'success',
                'data' => $responseData
            ]);

        } catch (\Exception $e) {
            Log::error('获取添加学校后详细信息异常: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return json([
                'code' => 1,
                'msg' => '获取学校详细信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取学校基本信息（用于添加学校后）
     * 主要从ba_school_basic_info表获取详细字段
     *
     * @param int $schoolId
     * @return array
     */
    private function getSchoolBasicInfoForAddSchool($schoolId)
    {
        try {
            $result = Db::name('school_basic_info')
                ->where('school_id', $schoolId)
                ->field('research_direction,exam_range,reference_books,retest_content,tuition_fee,study_years,accommodation,admission_requirements')
                ->find();

            if ($result) {
                $basicInfo = is_array($result) ? $result : $result->toArray();

                // 将exam_range映射到exam_subjects字段，保持一致性
                if (isset($basicInfo['exam_range'])) {
                    $basicInfo['exam_subjects'] = $basicInfo['exam_range'];
                }

                return $basicInfo;
            }

            return [];
        } catch (\Exception $e) {
            Log::error('获取学校基本信息异常: ' . $e->getMessage());
            return [];
        }
    }
}
